# In-App Purchase Setup Guide

This guide explains how to implement and use the beautiful in-app purchase system with export limits in your Resume Builder app.

## 🎯 Overview

The system automatically shows beautiful pricing plans when users hit their export limits, with:
- ✅ **Beautiful UI** - Gradient cards, badges, and smooth animations
- ✅ **Smart Triggers** - Shows only when needed (export limit reached)
- ✅ **Seamless Integration** - Works with existing export functionality
- ✅ **Real-time Updates** - Status updates immediately after purchase

## 🏗️ Architecture

### Core Components

1. **PurchaseCubit** - Manages purchase state and user subscription
2. **PricingPlansWidget** - Beautiful pricing cards with features
3. **ExportLimitDialog** - Shows when export limit is reached
4. **PurchaseManagerService** - Handles purchase flow and validation
5. **Export Integration** - Seamlessly integrated with resume export

### File Structure
```
lib/
├── presentation/
│   ├── cubits/purchase/
│   │   ├── purchase_cubit.dart
│   │   └── purchase_state.dart
│   ├── widgets/purchase/
│   │   ├── pricing_plans_widget.dart
│   │   └── export_limit_dialog.dart
│   └── pages/
│       ├── premium_upgrade_page.dart
│       └── export_with_purchase_page.dart
├── core/services/
│   └── purchase_manager_service.dart
└── domain/usecases/
    └── purchase_usecases.dart
```

## 🚀 Quick Setup

### Step 1: Initialize Purchase System

Add to your main app or home page:

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // ... other providers
        BlocProvider(create: (context) => sl<PurchaseCubit>()),
      ],
      child: MaterialApp(
        // ... app configuration
      ),
    );
  }
}
```

### Step 2: Add to Export Button

Replace your existing export button with:

```dart
BlocBuilder<ResumeCubit, ResumeState>(
  builder: (context, state) {
    return ElevatedButton.icon(
      onPressed: state.isExporting ? null : () => _handleExportWithLimits(),
      icon: const Icon(Icons.download),
      label: const Text('Export PDF'),
    );
  },
)

Future<void> _handleExportWithLimits() async {
  // Check if user can export
  final purchaseState = context.read<PurchaseCubit>().state;
  if (purchaseState is PurchaseLoaded && purchaseState.hasExportLimitReached) {
    // Show beautiful upgrade dialog
    final shouldProceed = await ExportLimitDialog.show(
      context,
      currentExports: purchaseState.exportStatus?.exportCount ?? 0,
      maxExports: 1,
      isPremiumUser: false,
    );
    
    if (shouldProceed != true) return;
  }
  
  // Proceed with export
  context.read<ResumeCubit>().exportToPdf();
}
```

### Step 3: Show Pricing Plans

Add anywhere in your app:

```dart
// Full page with header
const PricingPlansWidget(showHeader: true)

// Compact version without header
const PricingPlansWidget(showHeader: false)
```

## 🎨 UI Components

### 1. Pricing Plans Widget

Beautiful cards with:
- **Gradient headers** with badges (MOST POPULAR, BEST VALUE)
- **Feature lists** with checkmarks
- **Pricing display** with period information
- **Purchase buttons** with loading states
- **Restore purchases** functionality

### 2. Export Limit Dialog

Appears when limit is reached:
- **Warning header** with gradient background
- **Usage statistics** with progress bar
- **Embedded pricing plans** for immediate upgrade
- **Action buttons** (Maybe Later / Upgrade Now)

### 3. Status Indicators

Shows current status:
- **Premium badge** for premium users
- **Export count** with progress visualization
- **Subscription expiry** dates
- **Upgrade prompts** for free users

## 🔧 Configuration

### Remote Config Parameters

Set these in Firebase Remote Config:

```json
{
  "enable_export_limits": true,
  "max_free_exports": 1,
  "max_premium_exports": -1
}
```

### Product IDs

Create these in Google Play Console:

```
premium_monthly   - Monthly subscription
premium_yearly    - Yearly subscription (marked as MOST POPULAR)
premium_lifetime  - One-time purchase (marked as BEST VALUE)
```

## 📱 User Experience Flow

### Free User Journey

1. **User creates resume** ✅ (unlimited)
2. **User exports first PDF** ✅ (allowed)
3. **User tries second export** ❌ (limit reached)
4. **Beautiful dialog appears** 🎨 (upgrade prompt)
5. **User sees pricing plans** 💰 (in-dialog)
6. **User purchases premium** ✅ (seamless flow)
7. **User gets unlimited exports** 🚀 (immediate access)

### Premium User Journey

1. **User has premium subscription** ⭐
2. **User exports unlimited PDFs** ✅ (no restrictions)
3. **User sees premium status** 👑 (in UI)

## 🎯 Integration Examples

### Example 1: Simple Export Button

```dart
ElevatedButton(
  onPressed: () async {
    final canExport = await _checkExportLimits();
    if (canExport) {
      context.read<ResumeCubit>().exportToPdf();
    }
  },
  child: const Text('Export PDF'),
)
```

### Example 2: Export with Status Display

```dart
Column(
  children: [
    // Show current status
    BlocBuilder<PurchaseCubit, PurchaseState>(
      builder: (context, state) {
        if (state is PurchaseLoaded) {
          return Text(
            state.isPremiumUser 
              ? 'Premium: Unlimited exports'
              : 'Free: ${state.exportStatus?.exportCount ?? 0}/1 exports used',
          );
        }
        return const SizedBox.shrink();
      },
    ),
    
    // Export button
    ElevatedButton(
      onPressed: () => _handleExportWithLimits(),
      child: const Text('Export PDF'),
    ),
  ],
)
```

### Example 3: Upgrade Prompt Card

```dart
BlocBuilder<PurchaseCubit, PurchaseState>(
  builder: (context, state) {
    if (state is PurchaseLoaded && 
        !state.isPremiumUser && 
        state.hasExportLimitReached) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Text('Export limit reached!'),
              const SizedBox(height: 16),
              const PricingPlansWidget(showHeader: false),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  },
)
```

## 🔄 State Management

### Purchase States

- **PurchaseInitial** - Not initialized
- **PurchaseLoading** - Loading products/status
- **PurchaseLoaded** - Ready with products and user status
- **PurchaseError** - Error occurred

### Key Properties

```dart
// Check if user is premium
final isPremium = (state as PurchaseLoaded).isPremiumUser;

// Check if limit reached
final limitReached = (state as PurchaseLoaded).hasExportLimitReached;

// Get available products
final products = (state as PurchaseLoaded).products;

// Get export status
final exportStatus = (state as PurchaseLoaded).exportStatus;
```

## 🎨 Customization

### Colors and Themes

The UI automatically adapts to your app's theme, but you can customize:

```dart
// Pricing card colors
Colors.amber.shade50  // Popular plan background
Colors.green.shade50  // Lifetime plan background
Colors.blue           // Default plan color

// Status indicators
Colors.green          // Success/allowed
Colors.red            // Limit reached
Colors.amber          // Premium status
```

### Text and Messaging

Customize messages in:
- `PricingPlansWidget` - Plan titles and descriptions
- `ExportLimitDialog` - Limit reached messages
- `PurchaseCubit` - Success/error messages

## 🧪 Testing

### Test Scenarios

1. **Free user export flow**
   - Create resume
   - Export first PDF (should work)
   - Try second export (should show dialog)

2. **Purchase flow**
   - Click upgrade in dialog
   - Complete purchase
   - Verify premium status
   - Test unlimited exports

3. **Premium user flow**
   - Verify premium badge shows
   - Test unlimited exports
   - Check subscription status

### Test Accounts

Set up test accounts in Google Play Console for testing purchases without real charges.

## 🚨 Troubleshooting

### Common Issues

1. **Dialog not showing**
   - Check if `PurchaseCubit` is initialized
   - Verify export limits are enabled in remote config
   - Check user authentication

2. **Purchase not working**
   - Verify Google Play Console setup
   - Check product IDs match exactly
   - Ensure app is signed with release key

3. **Status not updating**
   - Call `purchaseCubit.refresh()` after export
   - Check Firebase connection
   - Verify user authentication

## 📈 Analytics

Track these events for insights:

```dart
// Export limit reached
Analytics.track('export_limit_reached', {
  'user_type': 'free',
  'export_count': exportCount,
});

// Upgrade dialog shown
Analytics.track('upgrade_dialog_shown', {
  'trigger': 'export_limit',
});

// Purchase completed
Analytics.track('purchase_completed', {
  'product_id': productId,
  'price': price,
});
```

## 🎉 Success!

Your beautiful in-app purchase system is now ready! Users will see gorgeous pricing plans exactly when they need them, creating a smooth path from free to premium. 🚀

The system handles all the complexity while providing a delightful user experience that encourages upgrades naturally.
