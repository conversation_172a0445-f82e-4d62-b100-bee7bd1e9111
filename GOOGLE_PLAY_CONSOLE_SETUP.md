# Google Play Console In-App Purchase Setup

This guide explains how to set up in-app purchases in Google Play Console for the Resume Builder app.

## Prerequisites

1. Google Play Console account
2. App uploaded to Google Play Console (at least as Internal Testing)
3. Merchant account linked to Google Play Console

## Step 1: Create In-App Products

### 1.1 Navigate to In-App Products
1. Open [Google Play Console](https://play.google.com/console)
2. Select your app
3. Go to **Monetize** → **Products** → **In-app products**

### 1.2 Create Premium Monthly Subscription
1. Click **"Create product"**
2. Fill in the details:
   ```
   Product ID: premium_monthly
   Name: Premium Monthly
   Description: Get unlimited PDF exports and premium features for one month
   Status: Active
   Price: $4.99 (or your preferred price)
   ```

### 1.3 Create Premium Yearly Subscription
1. Click **"Create product"**
2. Fill in the details:
   ```
   Product ID: premium_yearly
   Name: Premium Yearly
   Description: Get unlimited PDF exports and premium features for one year. Best value!
   Status: Active
   Price: $29.99 (or your preferred price)
   ```

### 1.4 Create Premium Lifetime Purchase
1. Click **"Create product"**
2. Fill in the details:
   ```
   Product ID: premium_lifetime
   Name: Premium Lifetime
   Description: Get unlimited PDF exports and premium features forever with a one-time payment
   Status: Active
   Price: $99.99 (or your preferred price)
   ```

## Step 2: Configure Subscriptions (Optional)

If you want to use subscriptions instead of one-time purchases:

### 2.1 Create Subscription Group
1. Go to **Monetize** → **Products** → **Subscriptions**
2. Click **"Create subscription"**
3. Create a subscription group: "Premium Subscriptions"

### 2.2 Add Base Plans
For each subscription (monthly/yearly):
1. Set the billing period
2. Set the price
3. Configure free trial (optional)
4. Set up grace period and account hold

## Step 3: Set Up License Testing

### 3.1 Add Test Accounts
1. Go to **Setup** → **License testing**
2. Add test Gmail accounts that can make test purchases
3. Set license response to "RESPOND_NORMALLY"

### 3.2 Create Test Tracks
1. Go to **Testing** → **Internal testing**
2. Create a new release
3. Upload your APK/AAB
4. Add test users

## Step 4: App Configuration

### 4.1 Update Product IDs in Code
Make sure your product IDs in the app match exactly:

```dart
// In lib/core/services/in_app_purchase_service.dart
static const String premiumMonthlyId = 'premium_monthly';
static const String premiumYearlyId = 'premium_yearly';
static const String premiumLifetimeId = 'premium_lifetime';
```

### 4.2 Build and Upload
1. Build your app: `flutter build appbundle`
2. Upload to Google Play Console
3. Publish to Internal Testing track

## Step 5: Testing

### 5.1 Test Purchase Flow
1. Install app from Internal Testing
2. Sign in with test account
3. Try purchasing each product
4. Verify purchases appear in Google Play Console

### 5.2 Test Purchase Verification
1. Check that purchases are saved to Firestore
2. Verify premium status is updated
3. Test export limits are removed

## Step 6: Production Setup

### 6.1 App Review
1. Complete Google Play's app review process
2. Ensure your app complies with Google Play policies
3. Add privacy policy and terms of service

### 6.2 Publish
1. Move from Internal Testing to Production
2. Monitor purchase analytics
3. Set up purchase notifications (optional)

## Important Notes

### Product ID Naming
- Use lowercase with underscores
- Keep them simple and descriptive
- Don't change them after publishing

### Pricing Strategy
- Research competitor pricing
- Consider regional pricing
- Test different price points

### Testing Checklist
- [ ] Products appear in app
- [ ] Purchase flow works
- [ ] Premium features unlock
- [ ] Export limits are removed
- [ ] Restore purchases works
- [ ] Error handling works

## Troubleshooting

### Common Issues

1. **Products not loading**
   - Check product IDs match exactly
   - Ensure app is signed with release key
   - Verify products are active in console

2. **Test purchases not working**
   - Use test account added to license testing
   - Install from Internal Testing track
   - Clear Google Play Store cache

3. **Purchase verification failing**
   - Check Firebase security rules
   - Verify user authentication
   - Check network connectivity

### Debug Commands

```bash
# Check if products are available
adb logcat | grep "IAP"

# Clear Google Play Store data
adb shell pm clear com.android.vending
```

## Security Considerations

1. **Server-side verification** (Recommended for production)
   - Implement receipt validation on your server
   - Use Google Play Developer API
   - Prevent purchase fraud

2. **Firestore Security Rules**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /purchases/{purchaseId} {
         allow read, write: if request.auth != null && 
           request.auth.uid == resource.data.userId;
       }
       match /user_subscriptions/{userId} {
         allow read, write: if request.auth != null && 
           request.auth.uid == userId;
       }
     }
   }
   ```

## Analytics and Monitoring

1. **Track purchase events** in Firebase Analytics
2. **Monitor subscription metrics** in Google Play Console
3. **Set up alerts** for purchase failures
4. **Review user feedback** regularly

## Support

For issues with Google Play Console:
- [Google Play Console Help](https://support.google.com/googleplay/android-developer)
- [In-app billing documentation](https://developer.android.com/google/play/billing)

For Flutter in_app_purchase issues:
- [Flutter in_app_purchase documentation](https://pub.dev/packages/in_app_purchase)
- [Flutter GitHub issues](https://github.com/flutter/flutter/issues)
