import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../presentation/cubits/purchase/purchase_cubit.dart';
import '../presentation/widgets/purchase/pricing_plans_widget.dart';
import '../core/di/injection_container.dart';

class PricingDebugPage extends StatefulWidget {
  const PricingDebugPage({super.key});

  @override
  State<PricingDebugPage> createState() => _PricingDebugPageState();
}

class _PricingDebugPageState extends State<PricingDebugPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pricing Debug'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<PurchaseCubit>().initialize();
            },
          ),
        ],
      ),
      body: BlocProvider(
        create: (context) => getIt<PurchaseCubit>()..initialize(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDebugInfo(),
              const SizedBox(height: 24),
              _buildPricingSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDebugInfo() {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.bug_report, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Debug Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            BlocBuilder<PurchaseCubit, PurchaseState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDebugRow('Current State:', state.runtimeType.toString()),
                    if (state is PurchaseLoaded) ...[
                      _buildDebugRow('Products Count:', '${state.products.length}'),
                      _buildDebugRow('Is Premium:', '${state.isPremiumUser}'),
                      _buildDebugRow('Has Subscription:', '${state.subscription != null}'),
                      _buildDebugRow('Export Status:', '${state.exportStatus?.exportCount ?? 'null'}'),
                      if (state.products.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        const Text('Available Products:', style: TextStyle(fontWeight: FontWeight.bold)),
                        ...state.products.map((product) => 
                          Padding(
                            padding: const EdgeInsets.only(left: 16, top: 4),
                            child: Text('• ${product.id} - ${product.title} (${product.price})'),
                          ),
                        ),
                      ],
                    ],
                    if (state is PurchaseError) ...[
                      _buildDebugRow('Error:', state.message),
                    ],
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () => context.read<PurchaseCubit>().initialize(),
                          child: const Text('Initialize'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () => context.read<PurchaseCubit>().refresh(),
                          child: const Text('Refresh'),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.shopping_cart, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Pricing Plans Widget',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'This is what users should see when pricing plans load:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const PricingPlansWidget(showHeader: true),
            ),
          ],
        ),
      ),
    );
  }
}
