import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/services/in_app_purchase_service.dart';
import '../domain/usecases/purchase_usecases.dart';
import '../core/di/injection_container.dart';

class PurchaseDebugPage extends StatefulWidget {
  const PurchaseDebugPage({super.key});

  @override
  State<PurchaseDebugPage> createState() => _PurchaseDebugPageState();
}

class _PurchaseDebugPageState extends State<PurchaseDebugPage> {
  final PurchaseUseCases _purchaseUseCases = getIt<PurchaseUseCases>();
  final InAppPurchaseService _purchaseService = InAppPurchaseService();
  
  List<String> _debugLogs = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addLog('Purchase Debug Page initialized');
  }

  void _addLog(String message) {
    setState(() {
      _debugLogs.add('${DateTime.now().toIso8601String()}: $message');
    });
    debugPrint('DEBUG: Purchase Debug - $message');
  }

  Future<void> _testInitialization() async {
    setState(() {
      _isLoading = true;
      _debugLogs.clear();
    });

    _addLog('🔄 Starting initialization test...');

    try {
      // Test service availability
      _addLog('📱 Checking if in-app purchases are available...');
      final isAvailable = await _purchaseService.isAvailable();
      _addLog('📱 In-app purchases available: $isAvailable');

      if (!isAvailable) {
        _addLog('❌ In-app purchases not available on this device');
        setState(() => _isLoading = false);
        return;
      }

      // Test initialization
      _addLog('🚀 Initializing purchase system...');
      final initialized = await _purchaseUseCases.initialize();
      _addLog('🚀 Purchase system initialized: $initialized');

      if (!initialized) {
        _addLog('❌ Failed to initialize purchase system');
        setState(() => _isLoading = false);
        return;
      }

      // Test product loading
      _addLog('📦 Loading available products...');
      final products = await _purchaseUseCases.getAvailableProducts();
      _addLog('📦 Found ${products.length} products');

      if (products.isEmpty) {
        _addLog('⚠️  No products found! Check Google Play Console setup.');
        _addLog('🔍 Expected products: ${InAppPurchaseService.productIds.join(', ')}');
      } else {
        for (final product in products) {
          _addLog('✅ Product: ${product.id} - ${product.title} (${product.price})');
        }
      }

      // Check for missing products
      final expectedProducts = InAppPurchaseService.productIds;
      final foundProductIds = products.map((p) => p.id).toSet();
      final missingProducts = expectedProducts.where((id) => !foundProductIds.contains(id)).toList();
      
      if (missingProducts.isNotEmpty) {
        _addLog('❌ Missing products in Google Play Console:');
        for (final missing in missingProducts) {
          _addLog('   - $missing');
        }
      }

      _addLog('✅ Initialization test completed');
    } catch (e) {
      _addLog('💥 Error during initialization: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _testProductQuery() async {
    _addLog('🔍 Testing direct product query...');
    
    try {
      final service = InAppPurchaseService();
      await service.initialize();
      
      final products = service.availableProducts;
      _addLog('🔍 Direct query found ${products.length} products');
      
      for (final product in products) {
        _addLog('🔍 Direct: ${product.id} - ${product.title}');
      }
    } catch (e) {
      _addLog('💥 Direct query error: $e');
    }
  }

  void _copyLogsToClipboard() {
    final logsText = _debugLogs.join('\n');
    Clipboard.setData(ClipboardData(text: logsText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Debug logs copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Purchase Debug'),
        actions: [
          IconButton(
            onPressed: _copyLogsToClipboard,
            icon: const Icon(Icons.copy),
            tooltip: 'Copy logs to clipboard',
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testInitialization,
                    child: _isLoading
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Testing...'),
                            ],
                          )
                        : const Text('Test Purchase System'),
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testProductQuery,
                    child: const Text('Test Direct Product Query'),
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Expected Products:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        ...InAppPurchaseService.productIds.map(
                          (id) => Text('• $id'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _debugLogs.length,
              itemBuilder: (context, index) {
                final log = _debugLogs[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Text(
                    log,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
