import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../data/models/resume_template_model.dart';
import '../../cubits/resume/resume_cubit.dart';
import 'resume_preview_widget.dart';
import '../../../data/models/user_export_count_model.dart';
import '../../../core/services/purchase_manager_service.dart';

class ResumePreviewDialog extends StatelessWidget {
  final ResumeModel resume;
  final ResumeTemplateModel? template;

  const ResumePreviewDialog({
    super.key,
    required this.resume,
    this.template,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Resume Preview'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: () => _exportToPdf(context),
              tooltip: 'Export to PDF',
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _shareResume(context),
              tooltip: 'Share Resume',
            ),
          ],
        ),
        body: Container(
          color: Colors.grey.shade100,
          child: Center(
            child: Container(
              width: 595, // A4 width in points
              margin: const EdgeInsets.all(20),
              child: ResumePreviewWidget(
                resume: resume,
                template: template,
                isFullScreen: true,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _exportToPdf(BuildContext context) async {
    try {
      context.read<ResumeCubit>().exportToPdf();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Resume exported successfully!'),
            ],
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (e is ExportLimitExceededException) {
        // Show pricing page instead of toast
        if (context.mounted) {
          await PurchaseManagerService.instance.checkExportPermissionAndShowUpgrade(context);
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text('Export failed: $e')),
                ],
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _shareResume(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
      ),
    );
  }
}

class ResumePreviewBottomSheet extends StatelessWidget {
  final ResumeModel resume;
  final ResumeTemplateModel? template;

  const ResumePreviewBottomSheet({
    super.key,
    required this.resume,
    this.template,
  });

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Row(
                  children: [
                    const Text(
                      'Resume Preview',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.fullscreen),
                      onPressed: () {
                        Navigator.of(context).pop();
                        showDialog(
                          context: context,
                          builder: (context) => ResumePreviewDialog(
                            resume: resume,
                            template: template,
                          ),
                        );
                      },
                      tooltip: 'Full Screen',
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  color: Colors.grey.shade100,
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Center(
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 500),
                        child: ResumePreviewWidget(
                          resume: resume,
                          template: template,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Helper function to show preview
void showResumePreview(BuildContext context, ResumeModel resume, {bool fullScreen = false, ResumeTemplateModel? template}) {
  if (fullScreen) {
    showDialog(
      context: context,
      builder: (context) => ResumePreviewDialog(
        resume: resume,
        template: template,
      ),
    );
  } else {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ResumePreviewBottomSheet(
        resume: resume,
        template: template,
      ),
    );
  }
}
