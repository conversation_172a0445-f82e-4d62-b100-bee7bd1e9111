import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/purchase_model.dart';
import '../../cubits/purchase/purchase_cubit.dart';

class PricingPlansWidget extends StatelessWidget {
  final bool showHeader;
  final VoidCallback? onPurchaseSuccess;

  const PricingPlansWidget({
    super.key,
    this.showHeader = true,
    this.onPurchaseSuccess,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PurchaseCubit, PurchaseState>(
      listener: (context, state) {
        if (state is PurchaseLoaded) {
          if (state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Colors.green,
              ),
            );
            onPurchaseSuccess?.call();
            context.read<PurchaseCubit>().clearMessages();
          }
          
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
            context.read<PurchaseCubit>().clearMessages();
          }
        }
      },
      builder: (context, state) {
        debugPrint('DEBUG: PricingPlansWidget - Current state: ${state.runtimeType}');

        if (state is PurchaseLoading) {
          debugPrint('DEBUG: PricingPlansWidget - Showing loading spinner');
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is PurchaseError) {
          debugPrint('DEBUG: PricingPlansWidget - Showing error: ${state.message}');
          return _buildErrorWidget(context, state.message);
        }

        if (state is PurchaseLoaded) {
          debugPrint('DEBUG: PricingPlansWidget - Loaded with ${state.products.length} products');
          return _buildPricingPlans(context, state);
        }

        debugPrint('DEBUG: PricingPlansWidget - State is PurchaseInitial, showing empty widget');
        return _buildInitialWidget(context);
      },
    );
  }

  Widget _buildInitialWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading pricing plans...',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Initializing purchase system',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<PurchaseCubit>().initialize(),
            child: const Text('Initialize Purchases'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String error) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Unable to load pricing plans',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<PurchaseCubit>().initialize(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingPlans(BuildContext context, PurchaseLoaded state) {
    if (state.products.isEmpty) {
      debugPrint('DEBUG: PricingPlansWidget - ❌ NO PRODUCTS FOUND!');
      debugPrint('DEBUG: PricingPlansWidget - This means products failed to load from Google Play Console');
      debugPrint('DEBUG: PricingPlansWidget - Check: 1) Products exist in Play Console 2) App is signed with release key 3) Testing from Internal Testing track');

      return Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'No pricing plans available',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Products not found in Google Play Console.\nCheck setup and try again.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.read<PurchaseCubit>().initialize(),
              child: const Text('Retry Loading'),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showHeader) _buildHeader(context, state),
        const SizedBox(height: 24),
        _buildPlanCards(context, state),
        const SizedBox(height: 24),
        _buildRestoreButton(context, state),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, PurchaseLoaded state) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber.shade400, Colors.orange.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.star,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Upgrade to Premium',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Unlock unlimited PDF exports and premium features',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          if (state.hasExportLimitReached) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.warning,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You\'ve reached your export limit. Upgrade to continue.',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlanCards(BuildContext context, PurchaseLoaded state) {
    // Sort products by preference (yearly, monthly, lifetime)
    final sortedProducts = List<PurchaseProductModel>.from(state.products);
    sortedProducts.sort((a, b) {
      const order = ['premium_yearly', 'premium_monthly', 'premium_lifetime'];
      return order.indexOf(a.id).compareTo(order.indexOf(b.id));
    });

    return Column(
      children: sortedProducts.map((product) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildPlanCard(context, product, state),
        );
      }).toList(),
    );
  }

  Widget _buildPlanCard(BuildContext context, PurchaseProductModel product, PurchaseLoaded state) {
    final isPopular = product.id == 'premium_yearly';
    final isLifetime = product.id == 'premium_lifetime';
    
    Color cardColor = Colors.white;
    Color borderColor = Colors.grey.shade300;
    String? badge;
    Color? badgeColor;

    if (isPopular) {
      cardColor = Colors.amber.shade50;
      borderColor = Colors.amber;
      badge = 'MOST POPULAR';
      badgeColor = Colors.amber;
    } else if (isLifetime) {
      cardColor = Colors.green.shade50;
      borderColor = Colors.green;
      badge = 'BEST VALUE';
      badgeColor = Colors.green;
    }

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        border: Border.all(color: borderColor, width: isPopular ? 2 : 1),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getPlanTitle(product.id),
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getPlanSubtitle(product.id),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          product.price,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isPopular ? Colors.amber.shade700 : Colors.green.shade700,
                          ),
                        ),
                        if (product.id != 'premium_lifetime')
                          Text(
                            _getPeriodText(product.id),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildFeaturesList(),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: state.isLoading 
                        ? null 
                        : () => context.read<PurchaseCubit>().purchaseProduct(product.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isPopular ? Colors.amber : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: state.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Choose ${_getPlanTitle(product.id)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
          if (badge != null)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: badgeColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(16),
                    bottomLeft: Radius.circular(16),
                  ),
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Unlimited PDF exports',
      'Premium resume templates',
      'Priority customer support',
      'Ad-free experience',
      'Cloud sync across devices',
    ];

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 18,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  feature,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRestoreButton(BuildContext context, PurchaseLoaded state) {
    return Center(
      child: TextButton(
        onPressed: state.isLoading 
            ? null 
            : () => context.read<PurchaseCubit>().restorePurchases(),
        child: const Text('Restore Previous Purchases'),
      ),
    );
  }

  String _getPlanTitle(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return 'Monthly Plan';
      case 'premium_yearly':
        return 'Yearly Plan';
      case 'premium_lifetime':
        return 'Lifetime Plan';
      default:
        return 'Premium Plan';
    }
  }

  String _getPlanSubtitle(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return 'Perfect for short-term use';
      case 'premium_yearly':
        return 'Save 60% with annual billing';
      case 'premium_lifetime':
        return 'One-time payment, lifetime access';
      default:
        return 'Unlock all premium features';
    }
  }

  String _getPeriodText(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return 'per month';
      case 'premium_yearly':
        return 'per year';
      default:
        return '';
    }
  }
}
