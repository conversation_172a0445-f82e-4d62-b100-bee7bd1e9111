import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../cubits/purchase/purchase_cubit.dart';
import '../cubits/resume/resume_cubit.dart';
import '../widgets/purchase/export_limit_dialog.dart';
import '../widgets/purchase/pricing_plans_widget.dart';

/// Example page showing how to integrate export functionality with purchase limits
class ExportWithPurchasePage extends StatefulWidget {
  const ExportWithPurchasePage({super.key});

  @override
  State<ExportWithPurchasePage> createState() => _ExportWithPurchasePageState();
}

class _ExportWithPurchasePageState extends State<ExportWithPurchasePage> {
  @override
  void initState() {
    super.initState();
    // Initialize purchase system when page loads
    context.read<PurchaseCubit>().initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Export Resume'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: BlocListener<PurchaseCubit, PurchaseState>(
        listener: (context, state) {
          if (state is PurchaseLoaded && state.successMessage != null) {
            // Purchase successful, user is now premium
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildExportSection(),
              const SizedBox(height: 32),
              _buildStatusSection(),
              const SizedBox(height: 32),
              _buildUpgradeSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExportSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.picture_as_pdf, color: Colors.red),
                const SizedBox(width: 12),
                Text(
                  'Export Resume',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Export your resume as a PDF file. Free users get limited exports, while premium users enjoy unlimited exports.',
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: BlocBuilder<ResumeCubit, ResumeState>(
                builder: (context, resumeState) {
                  return ElevatedButton.icon(
                    onPressed: resumeState.isExporting 
                        ? null 
                        : () => _handleExportAttempt(),
                    icon: resumeState.isExporting
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.download),
                    label: Text(
                      resumeState.isExporting ? 'Exporting...' : 'Export PDF',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return BlocBuilder<PurchaseCubit, PurchaseState>(
      builder: (context, state) {
        if (state is PurchaseLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (state is PurchaseLoaded) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        state.isPremiumUser ? Icons.star : Icons.info,
                        color: state.isPremiumUser ? Colors.amber : Colors.blue,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Account Status',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildStatusInfo(state),
                ],
              ),
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStatusInfo(PurchaseLoaded state) {
    final exportStatus = state.exportStatus;
    final subscription = state.subscription;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Subscription status
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: state.isPremiumUser ? Colors.amber.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: state.isPremiumUser ? Colors.amber : Colors.grey.shade300,
            ),
          ),
          child: Row(
            children: [
              Icon(
                state.isPremiumUser ? Icons.star : Icons.person,
                color: state.isPremiumUser ? Colors.amber : Colors.grey,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  state.isPremiumUser ? 'Premium User' : 'Free User',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: state.isPremiumUser ? Colors.amber.shade700 : Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        if (exportStatus != null) ...[
          const SizedBox(height: 16),
          // Export count info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Exports used:'),
              Text(
                state.isPremiumUser 
                    ? '${exportStatus.exportCount} (Unlimited)'
                    : '${exportStatus.exportCount} / 1',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          
          if (!state.isPremiumUser) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: exportStatus.exportCount / 1,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                exportStatus.exportCount >= 1 ? Colors.red : Colors.green,
              ),
            ),
          ],
        ],

        if (subscription != null && subscription.subscriptionEnd != null) ...[
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Subscription expires:'),
              Text(
                _formatDate(subscription.subscriptionEnd!),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildUpgradeSection() {
    return BlocBuilder<PurchaseCubit, PurchaseState>(
      builder: (context, state) {
        if (state is PurchaseLoaded && !state.isPremiumUser) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.upgrade, color: Colors.amber),
                      const SizedBox(width: 12),
                      Text(
                        'Upgrade to Premium',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const PricingPlansWidget(showHeader: false),
                ],
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> _handleExportAttempt() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showErrorDialog('Please sign in to export your resume');
        return;
      }

      // Check export status first
      final purchaseState = context.read<PurchaseCubit>().state;
      if (purchaseState is PurchaseLoaded) {
        final exportStatus = purchaseState.exportStatus;
        
        // If user is not premium and has reached limit, show upgrade dialog
        if (!purchaseState.isPremiumUser && 
            exportStatus != null && 
            exportStatus.exportCount >= 1) {
          
          final shouldProceed = await ExportLimitDialog.show(
            context,
            currentExports: exportStatus.exportCount,
            maxExports: 1,
            isPremiumUser: false,
          );

          if (shouldProceed != true) {
            return; // User declined or dismissed dialog
          }
        }
      }

      // Proceed with export
      if (mounted) {
        context.read<ResumeCubit>().exportToPdf();

        // Refresh purchase state after export
        context.read<PurchaseCubit>().refresh();
      }
      
    } catch (e) {
      _showErrorDialog(e.toString());
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
