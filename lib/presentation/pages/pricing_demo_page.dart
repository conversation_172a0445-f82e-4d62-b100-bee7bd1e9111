import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/purchase/purchase_cubit.dart';
import '../widgets/purchase/pricing_plans_widget.dart';
import '../widgets/purchase/export_limit_dialog.dart';
import '../../injection/injection_container.dart';

/// Demo page to showcase the beautiful pricing plans
class PricingDemoPage extends StatefulWidget {
  const PricingDemoPage({super.key});

  @override
  State<PricingDemoPage> createState() => _PricingDemoPageState();
}

class _PricingDemoPageState extends State<PricingDemoPage> {
  @override
  void initState() {
    super.initState();
    // Initialize purchase system when page loads
    context.read<PurchaseCubit>().initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pricing Plans Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<PurchaseCubit>().initialize(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDemoButtons(),
            const SizedBox(height: 24),
            _buildPricingPlansSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Demo Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Try these actions to see the pricing system in action:',
            ),
            const SizedBox(height: 16),
            
            // Show Export Limit Dialog
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showExportLimitDialog(),
                icon: const Icon(Icons.warning),
                label: const Text('Show Export Limit Dialog'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Refresh Purchase Data
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => context.read<PurchaseCubit>().initialize(),
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh Purchase Data'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Show Purchase Status
            BlocBuilder<PurchaseCubit, PurchaseState>(
              builder: (context, state) {
                String statusText = 'Loading...';
                Color statusColor = Colors.grey;
                
                if (state is PurchaseLoaded) {
                  if (state.isPremiumUser) {
                    statusText = 'Premium User ⭐';
                    statusColor = Colors.amber;
                  } else {
                    statusText = 'Free User';
                    statusColor = Colors.blue;
                  }
                } else if (state is PurchaseError) {
                  statusText = 'Error: ${state.message}';
                  statusColor = Colors.red;
                }
                
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    'Status: $statusText',
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingPlansSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber),
                const SizedBox(width: 12),
                Text(
                  'Beautiful Pricing Plans',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'These are the gorgeous pricing plans that users will see when they hit export limits:',
            ),
            const SizedBox(height: 24),
            
            // The beautiful pricing plans widget
            BlocProvider(
              create: (context) => sl<PurchaseCubit>()..initialize(),
              child: const PricingPlansWidget(showHeader: true),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showExportLimitDialog() async {
    // Show the export limit dialog as a demo
    final result = await ExportLimitDialog.show(
      context,
      currentExports: 1,
      maxExports: 1,
      isPremiumUser: false,
    );
    
    if (result == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('User chose to upgrade!'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('User dismissed the dialog'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
