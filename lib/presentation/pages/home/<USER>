import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubits/auth/auth_cubit.dart';
import '../../cubits/resume/resume_cubit.dart';
import '../../cubits/remote_config/remote_config_cubit.dart';
import '../auth/auth_page.dart';
import '../resume_builder/resume_builder_page.dart';
import '../resumes/my_resumes_page.dart';
import '../profile/profile_page.dart';
import '../templates/templates_page.dart';
import '../../widgets/common/shimmer_widgets.dart';
import '../../widgets/activity/recent_activity_widget.dart';
import '../activity/activity_page.dart';
import '../pricing_demo_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, authState) {
          if (authState.isLoading) {
            return const DashboardShimmer();
          }

          if (!authState.isAuthenticated) {
            return const AuthPage();
          }

          return const AuthenticatedHomePage();
        },
      ),
    );
  }
}

class AuthenticatedHomePage extends StatefulWidget {
  const AuthenticatedHomePage({super.key});

  @override
  State<AuthenticatedHomePage> createState() => _AuthenticatedHomePageState();
}

class _AuthenticatedHomePageState extends State<AuthenticatedHomePage> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Load the last resume on app startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ResumeCubit>().loadLastResume();
    });
  }

  List<Widget> _getPages(bool templatesEnabled) {
    final pages = [
      const DashboardPage(),
      const MyResumesPage(),
      const ResumeBuilderPage(),
    ];

    if (templatesEnabled) {
      pages.add(const TemplatesPage());
    }

    pages.add(const ProfilePage());
    return pages;
  }

  List<BottomNavigationBarItem> _getNavigationItems(bool templatesEnabled) {
    final items = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.folder),
        label: 'My Resumes',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.edit_document),
        label: 'Builder',
      ),
    ];

    if (templatesEnabled) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.file_present_outlined),
        label: 'Templates',
      ));
    }

    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.person),
      label: 'Profile',
    ));

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
      builder: (context, remoteConfigState) {
        // Default to true if remote config is not loaded yet
        final templatesEnabled = remoteConfigState is RemoteConfigLoaded
            ? context.read<RemoteConfigCubit>().isTemplatesEnabled()
            : true;

        // Debug logging
        print('Remote Config State: ${remoteConfigState.runtimeType}');
        print('Templates Enabled: $templatesEnabled');

        final pages = _getPages(templatesEnabled);
        final navigationItems = _getNavigationItems(templatesEnabled);

        // Adjust selected index if templates are disabled and current index is beyond valid range
        if (_selectedIndex >= pages.length) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _selectedIndex = pages.length - 1; // Go to Profile page
            });
          });
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('ATS Resume Builder'),
          ),
          body: pages[_selectedIndex],
          bottomNavigationBar: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            items: navigationItems,
          ),
        );
      },
    );
  }

}

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome back!',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _DashboardCard(
                  title: 'Create New Resume',
                  subtitle: 'Start building your resume',
                  icon: Icons.add_circle_outline,
                  onTap: () {
                    context.read<ResumeCubit>().createNewResume();
                    // Navigate to resume builder
                    Navigator.of(context).pushNamed('/resume-builder');
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _DashboardCard(
                  title: 'My Resumes',
                  subtitle: 'View and edit existing resumes',
                  icon: Icons.folder_outlined,
                  onTap: () {
                    // Navigate to resumes list
                    Navigator.of(context).pushNamed('/my-resumes');
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Test button for Remote Config
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pushNamed('/remote-config-test');
              },
              icon: const Icon(Icons.settings),
              label: const Text('Test Remote Config'),
            ),
          ),
          const SizedBox(height: 16),
          // Test button for Pricing Plans
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PricingDemoPage(),
                  ),
                );
              },
              icon: const Icon(Icons.star),
              label: const Text('View Pricing Plans'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              child: RecentActivityWidget(
                maxItems: 5,
                showHeader: true,
                onViewAll: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ActivityPage(),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _DashboardCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback onTap;

  const _DashboardCard({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ),
    );
  }
}




