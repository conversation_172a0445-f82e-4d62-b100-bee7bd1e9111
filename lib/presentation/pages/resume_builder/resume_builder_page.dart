import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubits/resume/resume_cubit.dart';
import '../../cubits/template/template_cubit.dart';
import '../../widgets/resume_sections/personal_info_section.dart';
import '../../widgets/resume_sections/summary_section.dart';
import '../../widgets/resume_sections/work_experience_section.dart';
import '../../widgets/resume_sections/education_section.dart';
import '../../widgets/resume_sections/skills_section.dart';
import '../../widgets/resume_sections/projects_section.dart';
import '../../widgets/resume_sections/certifications_section.dart';
import '../../widgets/resume_sections/languages_section.dart';
import '../../widgets/common/shimmer_widgets.dart';
import '../../widgets/resume_preview/resume_preview_dialog.dart';
import '../../../core/services/ad_service.dart';
import '../../cubits/remote_config/remote_config_cubit.dart';
import '../../../data/models/user_export_count_model.dart';
import '../../../core/services/purchase_manager_service.dart';

class ResumeBuilderPage extends StatefulWidget {
  const ResumeBuilderPage({super.key});

  @override
  State<ResumeBuilderPage> createState() => _ResumeBuilderPageState();
}

class _ResumeBuilderPageState extends State<ResumeBuilderPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final List<Tab> _tabs = const [
    Tab(text: 'Personal', icon: Icon(Icons.person)),
    Tab(text: 'Summary', icon: Icon(Icons.description)),
    Tab(text: 'Experience', icon: Icon(Icons.work)),
    Tab(text: 'Education', icon: Icon(Icons.school)),
    Tab(text: 'Skills', icon: Icon(Icons.star)),
    Tab(text: 'Projects', icon: Icon(Icons.code)),
    Tab(text: 'Certificates', icon: Icon(Icons.verified)),
    Tab(text: 'Languages', icon: Icon(Icons.language)),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _loadInterstitialAd();
  }

  void _loadInterstitialAd() {
    // Check if interstitial ads are enabled via Remote Config
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    if (remoteConfigCubit.shouldShowInterstitialAds()) {
      AdService.instance.loadInterstitialAd(
        onAdLoaded: () {
          // Show interstitial ad after a short delay when page loads
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              _showInterstitialAd();
            }
          });
        },
        onAdFailedToLoad: (error) {
          print('Interstitial ad failed to load: $error');
        },
      );
    }
  }

  void _showInterstitialAd() {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    if (remoteConfigCubit.shouldShowInterstitialAds()) {
      AdService.instance.showInterstitialAd(
        onAdDismissed: () {
          print('Interstitial ad dismissed');
        },
        onAdFailedToShow: () {
          print('Interstitial ad failed to show');
        },
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ResumeCubit, ResumeState>(
      listener: (context, state) {
        // Show success message when resume is saved
        if (state.lastSavedAt != null && !state.hasUnsavedChanges && !state.isSaving) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Resume saved successfully!'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }

        // Show error messages
        if (state.errorMessage != null) {
          final isCloudSyncError = state.errorMessage!.contains('Saved locally, but failed to sync with cloud');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    isCloudSyncError ? Icons.cloud_off : Icons.error,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isCloudSyncError
                          ? 'Saved locally. Will sync when online.'
                          : state.errorMessage!,
                    ),
                  ),
                ],
              ),
              backgroundColor: isCloudSyncError ? Colors.orange : Colors.red,
              duration: Duration(seconds: isCloudSyncError ? 4 : 6),
              action: isCloudSyncError
                  ? SnackBarAction(
                      label: 'Retry',
                      textColor: Colors.white,
                      onPressed: () {
                        context.read<ResumeCubit>().retrySync();
                      },
                    )
                  : null,
            ),
          );
          context.read<ResumeCubit>().clearError();
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const ResumeBuilderShimmer();
        }

        if (state.currentResume == null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.description_outlined,
                    size: 80,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Resume Selected',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create a new resume or load an existing one',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      context.read<ResumeCubit>().createNewResume();
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Create New Resume'),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Resume Builder'),
                if (state.lastSavedAt != null)
                  Text(
                    'Last saved: ${_formatSaveTime(state.lastSavedAt!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
            actions: [
              if (state.hasUnsavedChanges)
                IconButton(
                  icon: state.isSaving
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save),
                  onPressed: state.isSaving
                      ? null
                      : () {
                          context.read<ResumeCubit>().saveResume();
                        },
                  tooltip: 'Save Resume',
                ),
              if (state.errorMessage != null && state.errorMessage!.contains('Saved locally'))
                IconButton(
                  icon: const Icon(Icons.cloud_sync, color: Colors.orange),
                  onPressed: () {
                    context.read<ResumeCubit>().retrySync();
                  },
                  tooltip: 'Retry Cloud Sync',
                ),
              IconButton(
                icon: state.isExporting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.picture_as_pdf),
                onPressed: state.isExporting
                    ? null
                    : () async {
                        // Get the current template from TemplateCubit
                        final templateCubit = context.read<TemplateCubit>();
                        final currentTemplate = templateCubit.getCurrentTemplate();

                        print('DEBUG: PDF Export - Using template: ${currentTemplate.name} (ID: ${currentTemplate.id})');

                        try {
                          context.read<ResumeCubit>().exportToPdf(template: currentTemplate);
                        } catch (e) {
                          if (e is ExportLimitExceededException) {
                            // Show pricing page instead of toast
                            await PurchaseManagerService.instance.checkExportPermissionAndShowUpgrade(context);
                          }
                          // Other errors will be handled by the ResumeCubit and shown as toasts
                        }
                      },
                tooltip: 'Export to PDF',
              ),
              IconButton(
                icon: const Icon(Icons.preview),
                onPressed: state.currentResume != null
                    ? () => _showPreview(context, state)
                    : null,
                tooltip: 'Preview Resume',
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'preview':
                      _showPreview(context, state);
                      break;
                    case 'share':
                      // TODO: Implement share functionality
                      break;
                    case 'duplicate':
                      // TODO: Implement duplicate functionality
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'preview',
                    child: Row(
                      children: [
                        Icon(Icons.preview),
                        SizedBox(width: 8),
                        Text('Preview'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share),
                        SizedBox(width: 8),
                        Text('Share'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'duplicate',
                    child: Row(
                      children: [
                        Icon(Icons.copy),
                        SizedBox(width: 8),
                        Text('Duplicate'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: _tabs,
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: const [
              PersonalInfoSection(),
              SummarySection(),
              WorkExperienceSection(),
              EducationSection(),
              SkillsSection(),
              ProjectsSection(),
              CertificationsSection(),
              LanguagesSection(),
            ],
          ),
          floatingActionButton: state.hasUnsavedChanges
              ? FloatingActionButton.extended(
                  onPressed: state.isSaving
                      ? null
                      : () {
                          context.read<ResumeCubit>().saveResume();
                        },
                  icon: state.isSaving
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.save),
                  label: Text(state.isSaving ? 'Saving...' : 'Save Changes'),
                )
              : null,
        );
      },
    );
  }

  void _showPreview(BuildContext context, ResumeState state) {
    if (state.currentResume == null) return;

    // Get the current template from TemplateCubit
    final templateCubit = context.read<TemplateCubit>();
    final currentTemplate = templateCubit.getCurrentTemplate();

    print('DEBUG: Preview - Using template: ${currentTemplate.name} (ID: ${currentTemplate.id})');

    showResumePreview(
      context,
      state.currentResume!,
      fullScreen: true,
      template: currentTemplate,
    );
  }

  String _formatSaveTime(DateTime saveTime) {
    final now = DateTime.now();
    final difference = now.difference(saveTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
