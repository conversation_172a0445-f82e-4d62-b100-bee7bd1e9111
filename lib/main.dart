import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/theme/app_theme.dart';
import 'core/services/ad_service.dart';
import 'injection/injection_container.dart' as di;
import 'presentation/pages/home/<USER>';
import 'presentation/pages/resumes/my_resumes_page.dart';
import 'presentation/pages/resume_builder/resume_builder_page.dart';
import 'presentation/pages/remote_config_demo_page.dart';
import 'presentation/pages/remote_config_test_page.dart';
import 'presentation/pages/ads_test_page.dart';
import 'presentation/cubits/theme/theme_cubit.dart';
import 'presentation/cubits/resume/resume_cubit.dart';
import 'presentation/cubits/auth/auth_cubit.dart';
import 'presentation/cubits/activity/activity_cubit.dart';
import 'presentation/cubits/template/template_cubit.dart';
import 'presentation/cubits/remote_config/remote_config_cubit.dart';
import 'presentation/cubits/purchase/purchase_cubit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize Mobile Ads
    await AdService.initialize();

    // Initialize Hive
    await Hive.initFlutter();

    // Initialize dependency injection
    await di.init();
  } catch (e) {
    print('Error during app initialization: $e');
    // Continue with app launch even if some services fail to initialize
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeCubit>(
          create: (context) => di.sl<ThemeCubit>()..loadTheme(),
        ),
        BlocProvider<AuthCubit>(
          create: (context) => di.sl<AuthCubit>()..checkAuthStatus(),
        ),
        BlocProvider<ResumeCubit>(
          create: (context) => di.sl<ResumeCubit>(),
        ),
        BlocProvider<ActivityCubit>(
          create: (context) => di.sl<ActivityCubit>(),
        ),
        BlocProvider<TemplateCubit>(
          create: (context) => di.sl<TemplateCubit>()..loadTemplates(),
        ),
        BlocProvider<RemoteConfigCubit>(
          create: (context) => di.sl<RemoteConfigCubit>()..initialize(),
        ),
        BlocProvider<PurchaseCubit>(
          create: (context) => di.sl<PurchaseCubit>(),
        ),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp(
            title: 'ATS Resume Builder',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeState.themeMode,
            home: const HomePage(),
            routes: {
              '/my-resumes': (context) => const MyResumesPage(),
              '/resume-builder': (context) => const ResumeBuilderPage(),
              '/remote-config-demo': (context) => const RemoteConfigDemoPage(),
              '/remote-config-test': (context) => const RemoteConfigTestPage(),
              '/ads-test': (context) => const AdsTestPage(),
            },
          );
        },
      ),
    );
  }
}
