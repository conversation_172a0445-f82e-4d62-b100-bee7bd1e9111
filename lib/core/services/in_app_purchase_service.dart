import 'dart:async';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../data/models/purchase_model.dart';

class InAppPurchaseService {
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // Product IDs - these should match your Google Play Console products
  static const String premiumMonthlyId = 'premium_monthly';
  static const String premiumYearlyId = 'premium_yearly';
  static const String premiumLifetimeId = 'premium_lifetime';
  
  static const List<String> productIds = [
    premiumMonthlyId,
    premiumYearlyId,
    premiumLifetimeId,
  ];

  // Stream controllers for purchase events
  final StreamController<List<PurchaseProductModel>> _productsController = 
      StreamController<List<PurchaseProductModel>>.broadcast();
  final StreamController<PurchaseModel> _purchaseController = 
      StreamController<PurchaseModel>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<List<PurchaseProductModel>> get productsStream => _productsController.stream;
  Stream<PurchaseModel> get purchaseStream => _purchaseController.stream;
  Stream<String> get errorStream => _errorController.stream;

  bool _isInitialized = false;
  List<PurchaseProductModel> _availableProducts = [];

  /// Initialize the in-app purchase service
  Future<bool> initialize() async {
    try {
      debugPrint('DEBUG: IAP - Initializing In-App Purchase service');
      
      final bool isAvailable = await _inAppPurchase.isAvailable();
      if (!isAvailable) {
        debugPrint('DEBUG: IAP - In-App Purchase not available');
        _errorController.add('In-App Purchase not available on this device');
        return false;
      }

      // Note: Pending purchases are automatically enabled in newer versions

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates,
        onError: (error) {
          debugPrint('DEBUG: IAP - Purchase stream error: $error');
          _errorController.add('Purchase error: $error');
        },
      );

      // Load available products
      await _loadProducts();

      _isInitialized = true;
      debugPrint('DEBUG: IAP - Service initialized successfully');
      return true;
    } catch (e) {
      debugPrint('DEBUG: IAP - Initialization error: $e');
      _errorController.add('Failed to initialize purchases: $e');
      return false;
    }
  }

  /// Load available products from the store
  Future<void> _loadProducts() async {
    try {
      debugPrint('DEBUG: IAP - Loading products: $productIds');
      
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(
        productIds.toSet(),
      );

      if (response.error != null) {
        debugPrint('DEBUG: IAP - Product query error: ${response.error}');
        _errorController.add('Failed to load products: ${response.error!.message}');
        return;
      }

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('DEBUG: IAP - Products not found: ${response.notFoundIDs}');
      }

      _availableProducts = response.productDetails
          .map((product) => PurchaseProductModel.fromProductDetails(product))
          .toList();

      debugPrint('DEBUG: IAP - Loaded ${_availableProducts.length} products');
      _productsController.add(_availableProducts);
    } catch (e) {
      debugPrint('DEBUG: IAP - Load products error: $e');
      _errorController.add('Failed to load products: $e');
    }
  }

  /// Handle purchase updates from the store
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('DEBUG: IAP - Purchase update: ${purchaseDetails.status} for ${purchaseDetails.productID}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          debugPrint('DEBUG: IAP - Purchase pending: ${purchaseDetails.productID}');
          break;
        case PurchaseStatus.purchased:
          debugPrint('DEBUG: IAP - Purchase completed: ${purchaseDetails.productID}');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.error:
          debugPrint('DEBUG: IAP - Purchase error: ${purchaseDetails.error}');
          _errorController.add('Purchase failed: ${purchaseDetails.error?.message ?? "Unknown error"}');
          break;
        case PurchaseStatus.restored:
          debugPrint('DEBUG: IAP - Purchase restored: ${purchaseDetails.productID}');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          debugPrint('DEBUG: IAP - Purchase canceled: ${purchaseDetails.productID}');
          break;
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    // Create purchase model (userId will be set by the calling code)
    final purchase = PurchaseModel.fromPurchaseDetails(purchaseDetails, '');
    _purchaseController.add(purchase);
  }

  /// Get available products
  List<PurchaseProductModel> get availableProducts => _availableProducts;

  /// Purchase a product
  Future<bool> purchaseProduct(String productId) async {
    try {
      if (!_isInitialized) {
        debugPrint('DEBUG: IAP - Service not initialized');
        _errorController.add('Purchase service not initialized');
        return false;
      }

      debugPrint('DEBUG: IAP - Attempting to purchase: $productId');

      // Find the product
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails({productId});
      
      if (response.productDetails.isEmpty) {
        debugPrint('DEBUG: IAP - Product not found: $productId');
        _errorController.add('Product not found: $productId');
        return false;
      }

      final ProductDetails productDetails = response.productDetails.first;
      
      // Create purchase param
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      // Start the purchase
      bool success;
      if (productIds.contains(productId)) {
        // For subscriptions or non-consumables
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        success = await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      }

      debugPrint('DEBUG: IAP - Purchase initiated: $success');
      return success;
    } catch (e) {
      debugPrint('DEBUG: IAP - Purchase error: $e');
      _errorController.add('Purchase failed: $e');
      return false;
    }
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    try {
      debugPrint('DEBUG: IAP - Restoring purchases');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('DEBUG: IAP - Restore error: $e');
      _errorController.add('Failed to restore purchases: $e');
    }
  }

  /// Get product by ID
  PurchaseProductModel? getProductById(String productId) {
    try {
      return _availableProducts.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Dispose the service
  void dispose() {
    _subscription.cancel();
    _productsController.close();
    _purchaseController.close();
    _errorController.close();
  }
}
