import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/usecases/purchase_usecases.dart';
import '../../domain/usecases/user_export_usecases.dart';
import '../../domain/repositories/remote_config_repository.dart';
import '../../presentation/cubits/purchase/purchase_cubit.dart';
import '../../presentation/pages/export_limit_reached_page.dart';
import '../../presentation/pages/premium_upgrade_page.dart';
import '../../data/models/user_export_count_model.dart';

class PurchaseManagerService {
  static PurchaseManagerService? _instance;

  final PurchaseUseCases _purchaseUseCases;
  final UserExportUseCases _userExportUseCases;
  final RemoteConfigRepository _remoteConfigRepository;

  PurchaseManagerService._(
    this._purchaseUseCases,
    this._userExportUseCases,
    this._remoteConfigRepository,
  );

  /// Initialize the purchase manager with dependencies
  static void initialize({
    required PurchaseUseCases purchaseUseCases,
    required UserExportUseCases userExportUseCases,
    required RemoteConfigRepository remoteConfigRepository,
  }) {
    _instance = PurchaseManagerService._(
      purchaseUseCases,
      userExportUseCases,
      remoteConfigRepository,
    );
  }

  /// Get the singleton instance
  static PurchaseManagerService get instance {
    if (_instance == null) {
      throw Exception('PurchaseManagerService not initialized. Call initialize() first.');
    }
    return _instance!;
  }

  /// Check if user can export and show upgrade dialog if needed
  Future<bool> checkExportPermissionAndShowUpgrade(BuildContext context) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get remote config
      final config = _remoteConfigRepository.getConfig();
      if (!config.enableExportLimits) {
        return true; // Export limits disabled
      }

      // Check if user can export
      final canExport = await _userExportUseCases.canUserExport(
        user.uid,
        config.maxFreeExports,
        config.maxPremiumExports,
      );

      if (canExport) {
        return true; // User can export
      }

      // User cannot export, show upgrade dialog
      if (!context.mounted) return false;
      return await _showExportLimitDialog(context, user.uid, config.maxFreeExports);
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error checking export permission: $e');
      // In case of error, allow export to avoid blocking users
      return true;
    }
  }

  /// Show export limit dialog
  Future<bool> _showExportLimitDialog(
    BuildContext context,
    String userId,
    int maxFreeExports,
  ) async {
    try {
      // Get current export status
      final exportStatus = await _userExportUseCases.getUserExportCount(userId);
      final currentExports = exportStatus?.exportCount ?? 0;

      if (!context.mounted) return false;

      // Initialize purchase cubit if not already done
      final purchaseCubit = context.read<PurchaseCubit>();
      if (purchaseCubit.state is! PurchaseLoaded) {
        await purchaseCubit.initialize();
      }

      // Show the export limit reached page
      if (!context.mounted) return false;

      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => ExportLimitReachedPage(
            currentExports: currentExports,
            maxExports: maxFreeExports,
            lastExportDate: exportStatus?.lastExportDate != null
                ? _formatDate(exportStatus!.lastExportDate!)
                : null,
          ),
        ),
      );

      return result ?? false;
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error showing dialog: $e');
      return false;
    }
  }

  /// Show premium upgrade page
  static Future<bool?> showUpgradePage(BuildContext context) {
    return Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const PremiumUpgradePage(),
      ),
    );
  }

  /// Check and sync user's premium status
  Future<void> syncPremiumStatus(String userId) async {
    try {
      await _purchaseUseCases.syncSubscriptionWithExportSystem(userId);
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error syncing premium status: $e');
    }
  }

  /// Process export attempt with limit checking
  Future<bool> processExportAttempt(BuildContext context) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if user can export
      final canExport = await checkExportPermissionAndShowUpgrade(context);
      if (!canExport) {
        return false; // User declined upgrade or dialog was dismissed
      }

      // Get remote config
      final config = _remoteConfigRepository.getConfig();
      
      // If export limits are disabled, allow export
      if (!config.enableExportLimits) {
        return true;
      }

      // Validate and process the export
      await _userExportUseCases.processExportAttempt(
        user.uid,
        config.maxFreeExports,
        config.maxPremiumExports,
      );

      return true;
    } catch (e) {
      if (e is ExportLimitExceededException) {
        // This shouldn't happen if we checked properly, but handle it
        if (context.mounted) {
          final user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            await _showExportLimitDialog(context, user.uid, e.maxAllowed);
          }
        }
        return false;
      }
      
      debugPrint('DEBUG: Purchase Manager - Error processing export: $e');
      rethrow; // Re-throw other errors
    }
  }

  /// Get user's current export status
  Future<ExportStatusInfo> getExportStatus(String userId) async {
    try {
      final config = _remoteConfigRepository.getConfig();
      return await _userExportUseCases.getExportStatusInfo(
        userId,
        config.maxFreeExports,
        config.maxPremiumExports,
      );
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error getting export status: $e');
      // Return safe defaults
      return const ExportStatusInfo(
        canExport: true,
        currentCount: 0,
        maxAllowed: 1,
        isPremiumUser: false,
        remainingExports: 1,
      );
    }
  }

  /// Check if user should see upgrade prompts
  Future<bool> shouldShowUpgradePrompts(String userId) async {
    try {
      final exportStatus = await _userExportUseCases.getUserExportCount(userId);
      final subscription = await _purchaseUseCases.getUserSubscription(userId);
      
      // Show prompts if user is not premium and has used exports
      return (subscription == null || !subscription.isActive) && 
             (exportStatus?.exportCount ?? 0) > 0;
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error checking upgrade prompts: $e');
      return false;
    }
  }

  /// Handle successful purchase
  Future<void> handlePurchaseSuccess(String userId) async {
    try {
      // Sync premium status
      await syncPremiumStatus(userId);

      // Reset export count for new premium user (optional)
      // await _userExportUseCases.resetExportCount(userId);

      debugPrint('DEBUG: Purchase Manager - Purchase success handled for user: $userId');
    } catch (e) {
      debugPrint('DEBUG: Purchase Manager - Error handling purchase success: $e');
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
