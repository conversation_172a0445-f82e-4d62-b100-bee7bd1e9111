# PDF Export Limits System

This document explains the new PDF export limits system that has been implemented in the Resume Builder app.

## Overview

The system allows you to:
- Limit the number of PDF exports for free users
- Provide unlimited exports for premium users
- Configure limits remotely via Firebase Remote Config
- Track user export counts in Firestore
- Display export status to users

## Key Features

### 1. Remote Configuration
Export limits are controlled via Firebase Remote Config with these parameters:

- `max_free_exports` (default: 1) - Maximum exports for free users
- `max_premium_exports` (default: -1) - Maximum exports for premium users (-1 = unlimited)
- `enable_export_limits` (default: true) - Enable/disable the entire system

### 2. User Export Tracking
Each user's export count is stored in Firestore collection `user_export_counts`:

```json
{
  "userId": "user-id-here",
  "exportCount": 2,
  "lastExportDate": "2024-01-15T10:30:00Z",
  "resetDate": "2024-01-01T00:00:00Z",
  "isPremiumUser": false
}
```

### 3. Automatic Export Validation
When users try to export a PDF, the system:
1. Checks if export limits are enabled
2. Validates the user's current export count
3. Throws an exception if limit is exceeded
4. Increments the count after successful export

## Implementation Details

### Core Components

1. **UserExportCountModel** - Data model for tracking exports
2. **FirebaseUserExportDataSource** - Firestore operations
3. **UserExportRepository** - Business logic interface
4. **UserExportUseCases** - Use cases for export operations
5. **ExportStatusWidget** - UI component to show export status

### Key Methods

```dart
// Check if user can export
await userExportUseCases.canUserExport(userId, maxFree, maxPremium);

// Validate export attempt (throws exception if limit exceeded)
await userExportUseCases.validateExportAttempt(userId, maxFree, maxPremium);

// Get export status for UI
final status = await userExportUseCases.getExportStatusInfo(userId, maxFree, maxPremium);

// Export with limit checking (used in ResumeCubit)
await resumeUseCases.exportToPdfWithLimitCheck(resume, template: template);
```

### Exception Handling

When export limits are exceeded, an `ExportLimitExceededException` is thrown:

```dart
try {
  await resumeCubit.exportToPdf();
} catch (e) {
  if (e is ExportLimitExceededException) {
    // Show upgrade dialog or limit message
    showDialog(/* upgrade prompt */);
  }
}
```

## Configuration

### Firebase Remote Config Setup

Add these parameters to your Firebase Remote Config:

```json
{
  "max_free_exports": 1,
  "max_premium_exports": -1,
  "enable_export_limits": true
}
```

### Firestore Security Rules

Add rules for the `user_export_counts` collection:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /user_export_counts/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Usage Examples

### 1. Show Export Status Widget

```dart
// In your widget
ExportStatusWidget(
  showUpgradeButton: true,
  onUpgradePressed: () {
    // Navigate to premium upgrade page
  },
)
```

### 2. Check Export Status Before Showing Export Button

```dart
// In your build method
FutureBuilder<bool>(
  future: _canUserExport(),
  builder: (context, snapshot) {
    final canExport = snapshot.data ?? true;
    return IconButton(
      icon: Icon(Icons.picture_as_pdf),
      onPressed: canExport ? _exportToPdf : _showUpgradeDialog,
    );
  },
)
```

### 3. Handle Export Limit Exceptions

```dart
void _exportToPdf() async {
  try {
    await context.read<ResumeCubit>().exportToPdf();
    // Show success message
  } catch (e) {
    if (e is ExportLimitExceededException) {
      _showExportLimitDialog(e);
    } else {
      _showErrorDialog(e.toString());
    }
  }
}
```

## Testing

Run the included tests:

```bash
flutter test test/user_export_test.dart
```

The tests cover:
- Model serialization/deserialization
- Export count increment/reset logic
- Exception handling
- Remote config integration

## Migration

The system is designed to be backward compatible:
- If export tracking services are not available, it falls back to regular export
- If remote config is disabled, exports work normally
- Existing export functionality remains unchanged

## Admin Functions

### Reset User Export Count

```dart
final userExportUseCases = sl<UserExportUseCases>();
await userExportUseCases.resetExportCount(userId);
```

### Update Premium Status

```dart
await userExportUseCases.updatePremiumStatus(userId, true);
```

### Check Export Statistics

```dart
final exportData = await userExportUseCases.getUserExportCount(userId);
print('User has exported ${exportData?.exportCount ?? 0} times');
```

## Troubleshooting

### Common Issues

1. **Export limits not working**: Check if `enable_export_limits` is true in Remote Config
2. **Users can export unlimited**: Verify Firestore security rules and user authentication
3. **Export count not incrementing**: Check Firebase connection and error logs

### Debug Information

The system includes extensive debug logging. Look for logs starting with:
- `DEBUG: Getting export count for user:`
- `DEBUG: Validating export attempt for user:`
- `DEBUG: Export count incremented to:`

## Future Enhancements

Potential improvements:
- Monthly/weekly reset cycles
- Different limits for different user tiers
- Export analytics and reporting
- Bulk export operations
- Export history tracking
